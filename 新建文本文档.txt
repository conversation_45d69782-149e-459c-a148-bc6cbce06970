请优化完善并重构产品需求文档，满足以下要求： 1.移除aria2相关的下载方式， 只需要直接下载方式； 2. Manga不一定有多张图， illust也不一定只有一张图， 不应该根据图的张数判断类型而是应该使用获取到的Type进行判断； 3.移除cookie.txt， 所有配置都应在yaml配置文件中指定； 4.如果配置文件不存在则创建一个示例配置文件； 5.单个图片的作品和多个图片的作品保存路径及命名规则应分别独立指定； 6.为程序添加完善详细的中文注释； 7.所有提示等信息均应使用简体中文； 8.执行程序时如果无参数则默认进入交互模式； 9.程序应可以在获取下一个作品信息的同时下载上一个已获取信息的作品； 10.程序应提供美观完善的交互。


thread 'main' panicked at /home/<USER>/.cargo/registry/src/mirrors.aliyun.com-0671735e7cc7f5e7/tracing-subscriber-0.3.19/src/util.rs:91:14:
failed to set global default subscriber: SetGlobalDefaultError("a global default trace dispatcher has already been set")
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace